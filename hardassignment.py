todo = []
complete =[]
removed = []

while True:
    print("Add to todo 1.")
    print("Remove from todo 2.")
    print("Add to complete 3.")
    print("View all 4.")	
    print("Exit 5.")
    
    user_input =  int(input("Enter your choice: "))
    
    if user_input == 1:
        print("Add to todo")
        print("Current todo: ", todo)
        enter = input("Enter your todo: ")
        todo.append(enter)
    elif user_input == 2:
        print("Remove from todo")
        print("Current todo: ", todo)
        enter = input("Enter your todo you want to remove: ")
        if enter in todo:
            todo.remove(enter)
            removed.append(enter)
        else:
            print("Todo not found")
    elif user_input == 3:
        print("Add to complete")
        print("Current todo: ", todo)   
        print("Current complete: ", complete)
        enter = input("Enter your todo: ")
        if enter in todo:
            todo.remove(enter)
            complete.append(enter)
        else:
            print("Todo not found")
    elif user_input == 4:
        print("Todo: ", todo)
        print("Complete: ", complete)
        print("Removed: ", removed)
    elif user_input == 5:
        print("Exit")
        break
    else:
        print("Invalid input")
	